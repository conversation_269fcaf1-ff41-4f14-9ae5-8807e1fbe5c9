# CPU调度算法详解

## 基本概念

### CPU调度
**CPU调度**：操作系统决定哪个进程获得CPU使用权的过程，是多任务操作系统的核心功能。

### 调度时机
1. **进程终止**：当前进程执行完毕
2. **进程阻塞**：等待I/O或其他资源
3. **时间片用完**：抢占式调度
4. **高优先级进程就绪**：优先级调度

### 调度目标
- **公平性**：所有进程都能获得CPU时间
- **效率**：最大化CPU利用率
- **响应时间**：最小化用户等待时间
- **吞吐量**：单位时间完成更多任务
- **周转时间**：最小化进程完成时间

## 主要调度算法

### 1. 先来先服务（FCFS）

#### 算法原理
- 按进程到达顺序分配CPU
- 非抢占式调度
- 使用队列维护就绪进程

#### 优缺点
**优点**：
- 实现简单
- 公平，无饥饿问题

**缺点**：
- 平均等待时间长
- 不适合交互式系统
- 护航效应（短进程等待长进程）

#### 适用场景
- 批处理系统
- 进程执行时间相近的场景

### 2. 最短作业优先（SJF）

#### 算法原理
- 选择执行时间最短的进程
- 可以是抢占式（SRTF）或非抢占式
- 理论上最优的平均等待时间

#### 优缺点
**优点**：
- 最小化平均等待时间
- 提高系统吞吐量

**缺点**：
- 难以预测执行时间
- 可能导致长进程饥饿
- 实现复杂

#### 适用场景
- 批处理系统
- 能够预测执行时间的场景

### 3. 优先级调度

#### 算法原理
- 为每个进程分配优先级
- 高优先级进程优先执行
- 可以是静态或动态优先级

#### 优先级设置
- **静态优先级**：创建时确定，不变
- **动态优先级**：根据行为动态调整
- **优先级老化**：防止低优先级进程饥饿

#### 优缺点
**优点**：
- 灵活性强
- 可以满足不同需求
- 支持实时系统

**缺点**：
- 可能导致饥饿
- 优先级设置困难
- 实现复杂

### 4. 时间片轮转（RR）

#### 算法原理
- 每个进程分配固定时间片
- 时间片用完后切换到下一个进程
- 抢占式调度，循环执行

#### 时间片选择
- **太小**：上下文切换开销大
- **太大**：响应时间长，类似FCFS
- **典型值**：10-100毫秒

#### 优缺点
**优点**：
- 响应时间好
- 公平性强
- 适合交互式系统

**缺点**：
- 上下文切换开销
- 不考虑进程特性
- 平均周转时间可能较长

### 5. 多级队列调度

#### 算法原理
- 将进程分为不同类别
- 每个类别使用独立队列
- 不同队列使用不同调度算法

#### 队列分类
1. **系统进程**：最高优先级
2. **交互式进程**：高优先级，短时间片
3. **批处理进程**：低优先级，长时间片

#### 优缺点
**优点**：
- 针对不同类型进程优化
- 灵活性强
- 性能好

**缺点**：
- 实现复杂
- 可能导致饥饿
- 队列间调度策略复杂

### 6. 多级反馈队列（MLFQ）

#### 算法原理
- 多个优先级队列
- 进程可以在队列间移动
- 动态调整进程优先级

#### 工作机制
1. **新进程**：进入最高优先级队列
2. **时间片用完**：降级到下一级队列
3. **I/O阻塞**：可能提升优先级
4. **老化机制**：防止饥饿

#### 优缺点
**优点**：
- 自适应调整
- 兼顾响应时间和吞吐量
- 广泛应用

**缺点**：
- 实现复杂
- 参数调优困难
- 可能被恶意利用

## 实时调度算法

### 1. 速率单调调度（RM）
- **原理**：周期短的任务优先级高
- **适用**：静态优先级，周期性任务
- **可调度条件**：CPU利用率 ≤ ln(2) ≈ 69.3%

### 2. 最早截止时间优先（EDF）
- **原理**：截止时间最早的任务优先
- **适用**：动态优先级
- **可调度条件**：CPU利用率 ≤ 100%

### 3. 最少松弛时间优先（LST）
- **松弛时间**：截止时间 - 当前时间 - 剩余执行时间
- **原理**：松弛时间最少的任务优先

## 现代操作系统调度

### Linux CFS（完全公平调度器）
- **虚拟运行时间**：vruntime概念
- **红黑树**：维护就绪进程
- **公平性**：保证所有进程公平获得CPU时间

### Windows调度
- **32个优先级**：0-31优先级级别
- **时间片**：根据优先级动态调整
- **前台进程**：获得更多CPU时间

### macOS调度
- **Mach内核**：基于线程调度
- **QoS类别**：服务质量分类
- **能耗感知**：考虑电池使用

## 调度算法比较

### 性能指标对比

| 算法 | 平均等待时间 | 响应时间 | 吞吐量 | 公平性 |
|------|-------------|----------|--------|--------|
| FCFS | 差 | 差 | 中 | 好 |
| SJF | 最优 | 差 | 好 | 差 |
| 优先级 | 中 | 好 | 好 | 差 |
| RR | 中 | 好 | 中 | 好 |
| MLFQ | 好 | 好 | 好 | 好 |

### 适用场景对比

| 算法 | 批处理 | 交互式 | 实时系统 | 服务器 |
|------|--------|--------|----------|--------|
| FCFS | 适合 | 不适合 | 不适合 | 不适合 |
| SJF | 适合 | 不适合 | 不适合 | 适合 |
| 优先级 | 适合 | 适合 | 适合 | 适合 |
| RR | 不适合 | 适合 | 不适合 | 适合 |
| MLFQ | 适合 | 适合 | 不适合 | 适合 |

## 面试高频问题

### Q1: 常见的CPU调度算法有哪些？
1. **FCFS**：先来先服务，简单但效率低
2. **SJF**：最短作业优先，理论最优
3. **优先级调度**：灵活但可能饥饿
4. **时间片轮转**：公平，适合交互式
5. **多级反馈队列**：综合性能好

### Q2: 时间片大小如何选择？
- **太小**：上下文切换开销大，系统效率低
- **太大**：响应时间长，类似FCFS
- **合适大小**：通常10-100ms，根据系统特性调整
- **考虑因素**：上下文切换开销、响应时间要求

### Q3: 什么是饥饿现象？如何解决？
**饥饿现象**：低优先级进程长时间得不到CPU

**解决方案**：
1. **优先级老化**：随时间提高优先级
2. **公平调度**：保证最小CPU时间
3. **多级反馈队列**：动态调整优先级

### Q4: 抢占式和非抢占式调度的区别？
**抢占式调度**：
- 可以强制切换正在运行的进程
- 响应时间好，适合交互式系统
- 实现复杂，开销大

**非抢占式调度**：
- 进程主动放弃CPU才切换
- 实现简单，开销小
- 响应时间差，适合批处理

### Q5: Linux的CFS调度器原理？
**CFS**：完全公平调度器
- **虚拟运行时间**：vruntime记录进程运行时间
- **红黑树**：按vruntime排序，O(log n)复杂度
- **公平性**：选择vruntime最小的进程运行
- **权重机制**：nice值影响时间片分配

## 调度优化

### 系统级优化
1. **CPU亲和性**：绑定进程到特定CPU
2. **NUMA感知**：考虑内存访问延迟
3. **负载均衡**：在多CPU间分配负载

### 应用级优化
1. **进程优先级**：设置合适的nice值
2. **CPU绑定**：使用taskset绑定CPU
3. **实时优先级**：关键任务使用实时调度

### 性能监控
1. **CPU使用率**：监控整体和单核使用率
2. **负载平均值**：系统负载指标
3. **上下文切换**：监控切换频率
4. **调度延迟**：测量调度响应时间
