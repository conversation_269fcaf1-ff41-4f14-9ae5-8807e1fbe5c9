# 协程与进程线程的对比

## 三者定义对比

| 类型 | 定义 | 调度方式 | 内存占用 |
|------|------|----------|----------|
| **进程** | 资源分配基本单位 | 内核调度 | MB-GB级 |
| **线程** | CPU调度基本单位 | 内核调度 | KB-MB级 |
| **协程** | 用户态轻量级线程 | 用户态调度 | KB级 |

## 性能对比

| 指标 | 进程 | 线程 | 协程 |
|------|------|------|------|
| **创建开销** | 最大 | 中等 | 最小 |
| **切换开销** | 最大（切换页表） | 中等 | 最小（用户态） |
| **并发数量** | 百级 | 千级 | 百万级 |
| **栈大小** | 8MB | 2MB | 2-8KB |

## 调度机制差异

**进程/线程调度**：
- 内核调度器管理
- 抢占式调度
- 需要系统调用

**协程调度**：
- 用户态调度器（如Go的GPM）
- 协作式+抢占式
- 无需系统调用

## 通信方式

**进程间**：IPC（管道、消息队列、共享内存、信号量）
**线程间**：共享内存 + 同步原语（互斥锁、条件变量）
**协程间**：Channel（Go）或共享内存

## 面试高频问题

### Q1: 协程为什么比线程轻量？
1. **用户态调度**：无需内核参与，避免用户态/内核态切换
2. **小栈空间**：初始2-8KB，动态增长
3. **简单上下文**：只保存必要寄存器
4. **无系统调用**：创建和切换都在用户空间

### Q2: Go的GPM模型原理？
- **G**：Goroutine，用户态线程
- **P**：Processor，逻辑处理器，维护本地队列
- **M**：Machine，系统线程
- **调度**：M通过P从队列中取G执行，实现M:N调度

### Q3: 什么场景用协程/线程？
**协程适用**：
- 高并发I/O密集型（网络服务）
- 大量并发连接
- 异步编程

**线程适用**：
- CPU密集型计算
- 需要真正并行执行
- 利用多核优势

### Q4: 协程的缺点？
1. **调试困难**：栈信息复杂
2. **CPU密集型效率低**：无法利用多核
3. **语言支持**：需要语言原生支持
4. **学习成本**：需要理解异步编程模型
