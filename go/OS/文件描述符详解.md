# 文件描述符详解

## 核心概念

**文件描述符（FD）**：操作系统为每个打开的文件分配的非负整数标识符

### 基本特点
- **进程级别**：每个进程有独立的FD表
- **最小分配**：总是分配最小可用的FD号
- **整数标识**：通常从0开始的小整数

### 标准文件描述符
| FD | 名称 | 用途 | 常量 |
|----|------|------|------|
| **0** | 标准输入 | 键盘输入 | STDIN_FILENO |
| **1** | 标准输出 | 屏幕输出 | STDOUT_FILENO |
| **2** | 标准错误 | 错误输出 | STDERR_FILENO |

## 三级映射结构

```
进程FD表 → 系统文件表 → inode表
    ↓           ↓          ↓
   FD号      文件状态    文件元数据
```

### 1. 进程文件描述符表
- **位置**：进程PCB中
- **内容**：FD到文件表项的指针
- **特点**：进程私有，fork时复制

### 2. 系统文件表
- **内容**：文件偏移量、访问模式、引用计数
- **共享**：多个FD可指向同一文件表项
- **作用**：维护文件状态信息

### 3. inode表
- **内容**：文件大小、权限、时间戳、数据块位置
- **唯一性**：每个文件对应一个inode
- **持久性**：存储在磁盘上

## 文件描述符操作

### 打开文件流程
1. **调用open()**：指定文件路径和模式
2. **分配FD**：选择最小可用FD号
3. **创建映射**：建立三级映射关系
4. **返回FD**：返回给用户程序使用

### 复制文件描述符
```c
int dup(int oldfd);           // 复制到最小可用FD
int dup2(int oldfd, int newfd); // 复制到指定FD
```
**用途**：重定向、管道实现

### 关闭文件流程
1. **调用close()**：传入FD号
2. **清除映射**：从FD表中移除
3. **减少引用**：文件表项引用计数-1
4. **释放资源**：引用计数为0时释放

## 文件描述符限制

### 进程级限制
```bash
# 查看当前限制
ulimit -n

# 临时修改限制
ulimit -n 4096

# 永久修改：编辑 /etc/security/limits.conf
```

### 系统级限制
```bash
# 查看系统限制
cat /proc/sys/fs/file-max

# 查看当前使用情况
cat /proc/sys/fs/file-nr
```

### 常见问题
- **FD泄漏**：打开文件忘记关闭
- **达到限制**："Too many open files"错误
- **性能影响**：过多FD影响系统性能

## I/O多路复用

### select()
- **原理**：监控多个FD的状态变化
- **限制**：FD数量限制（通常1024）
- **效率**：O(n)，需要遍历所有FD
- **跨平台**：支持所有Unix系统

### poll()
- **改进**：无FD数量限制
- **数据结构**：pollfd结构体数组
- **效率**：仍然O(n)
- **优势**：比select更灵活

### epoll()（Linux）
- **高效**：O(1)时间复杂度
- **事件驱动**：只返回就绪的FD
- **模式**：
  - **LT（水平触发）**：条件满足时持续通知
  - **ET（边缘触发）**：状态变化时通知一次

## 特殊文件描述符

### 管道FD
- **匿名管道**：`pipe()`创建，父子进程通信
- **命名管道**：`mkfifo()`创建，无关进程通信

### 套接字FD
- **网络套接字**：TCP/UDP网络通信
- **Unix域套接字**：本地进程高效通信

### 设备FD
- **字符设备**：/dev/tty、/dev/null等
- **块设备**：/dev/sda等磁盘设备

## 调试和监控

### 查看进程FD使用
```bash
# 查看进程打开的文件
lsof -p <PID>

# 查看进程FD目录
ls -la /proc/<PID>/fd/

# 查看特定文件的使用者
lsof <filename>
```

### 系统级监控
```bash
# 系统FD使用情况（已分配/未使用/最大值）
cat /proc/sys/fs/file-nr

# 系统最大FD数
cat /proc/sys/fs/file-max
```

## 面试高频问题

### Q1: 文件描述符是什么？
**定义**：操作系统为进程中每个打开的文件分配的非负整数标识符
**作用**：进程通过FD来访问文件、套接字、管道等资源
**范围**：进程级别，每个进程有独立的FD表

### Q2: 标准文件描述符有哪些？
- **0 (stdin)**：标准输入，通常连接键盘
- **1 (stdout)**：标准输出，通常连接屏幕
- **2 (stderr)**：标准错误，通常连接屏幕
**特点**：进程启动时自动打开，可以重定向

### Q3: 文件描述符的三级映射结构？
```
进程FD表 → 系统文件表 → inode表
```
- **FD表**：进程私有，存储FD到文件表项的映射
- **文件表**：系统全局，存储文件状态和偏移量
- **inode表**：文件元数据，大小、权限、数据块位置

### Q4: dup和dup2的区别？
- **dup(fd)**：复制fd到最小可用的FD号
- **dup2(oldfd, newfd)**：复制oldfd到指定的newfd
**用途**：重定向、管道实现、shell命令执行

### Q5: 如何避免文件描述符泄漏？
1. **及时关闭**：使用完文件后立即close()
2. **RAII模式**：使用智能指针或析构函数自动关闭
3. **异常处理**：确保异常情况下也能关闭文件
4. **监控检查**：定期检查进程的FD使用情况

### Q6: select、poll、epoll的区别？
| 特性 | select | poll | epoll |
|------|--------|------|-------|
| **FD限制** | 1024 | 无限制 | 无限制 |
| **时间复杂度** | O(n) | O(n) | O(1) |
| **跨平台** | 是 | 是 | Linux专有 |
| **性能** | 低 | 中 | 高 |
| **适用场景** | 少量FD | 中等数量FD | 大量FD |

### Q7: 文件描述符限制如何调整？
**查看限制**：`ulimit -n`
**临时调整**：`ulimit -n 4096`
**永久调整**：修改`/etc/security/limits.conf`
**系统级限制**：`/proc/sys/fs/file-max`
