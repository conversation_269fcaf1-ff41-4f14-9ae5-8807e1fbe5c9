# 内存管理核心概念

## 虚拟内存 vs 物理内存

| 类型 | 定义 | 特点 | 作用 |
|------|------|------|------|
| **物理内存** | 实际RAM硬件 | 有限、直接访问 | 存储实际数据 |
| **虚拟内存** | 抽象内存空间 | 进程隔离、可扩展 | 提供统一地址空间 |

**虚拟内存优势**：
- 进程隔离：独立地址空间
- 内存保护：防止相互干扰
- 内存扩展：使用磁盘扩展
- 内存共享：多进程共享物理页

## 地址转换机制

**核心组件**：
- **MMU**：内存管理单元，硬件地址转换
- **页表**：虚拟页到物理页框映射
- **TLB**：转换后备缓冲器，加速转换

## 页面置换算法

| 算法 | 原理 | 优点 | 缺点 |
|------|------|------|------|
| **FIFO** | 替换最早页面 | 实现简单 | Belady异常 |
| **LRU** | 替换最久未用 | 性能好 | 实现复杂 |
| **Clock** | 引用位+循环 | 近似LRU，开销小 | 性能略差 |

## 内存分配算法

### 连续分配算法
- **首次适应**：第一个足够大的块（快速，外部碎片多）
- **最佳适应**：最小的足够大的块（利用率高，小碎片多）
- **最坏适应**：最大的空闲块（剩余块大，利用率低）

### 高级分配算法
- **伙伴系统**：2的幂次方分块，减少外部碎片
- **内存池**：预分配固定大小块，分配极快

## 垃圾回收算法

| 算法 | 原理 | 优点 | 缺点 |
|------|------|------|------|
| **标记-清除** | 标记可达对象，清除其他 | 不移动对象 | 内存碎片 |
| **复制算法** | 复制活跃对象到新区域 | 无碎片 | 内存利用率50% |
| **标记-压缩** | 标记后压缩消除碎片 | 高利用率 | 移动对象开销大 |

## 内存碎片问题

**内部碎片**：分配块内部未使用空间
**外部碎片**：空闲块太小无法分配

**解决方案**：
- 伙伴系统算法
- 内存池技术
- 垃圾回收压缩

## 面试高频问题

### Q1: 虚拟内存的作用？
1. **进程隔离**：每个进程独立地址空间
2. **内存保护**：防止进程间干扰
3. **内存扩展**：磁盘作为内存扩展
4. **简化编程**：统一的线性地址空间

### Q2: 页面置换算法选择？
- **FIFO**：实现简单，但可能出现异常
- **LRU**：性能最好，但实现复杂
- **Clock**：实际系统常用，平衡性能和复杂度

### Q3: 如何减少内存碎片？
1. **使用伙伴系统**：按2的幂分配
2. **内存池技术**：预分配固定大小
3. **垃圾回收压缩**：定期整理内存
4. **合理的分配策略**：避免频繁小块分配

### Q4: 内存泄漏检测方法？
- **静态分析**：代码审查和工具检查
- **动态检测**：运行时监控分配释放
- **工具辅助**：Valgrind、AddressSanitizer
- **性能监控**：内存使用趋势分析
