# 进程和线程的同步方式

## 同步机制适用性对比

| 同步方式 | 线程间 | 进程间 | 实现复杂度 | 性能 | 适用场景 |
|----------|--------|--------|------------|------|----------|
| **互斥锁** | ✓ | ✓(需特殊设置) | 低 | 高 | 互斥访问 |
| **读写锁** | ✓ | ✓(复杂) | 中 | 高 | 读多写少 |
| **信号量** | ✓ | ✓ | 低 | 高 | 资源计数 |
| **条件变量** | ✓ | ✓(复杂) | 高 | 高 | 复杂同步 |
| **自旋锁** | ✓ | ✓ | 低 | 极高 | 短时间锁定 |
| **屏障** | ✓ | ✗ | 中 | 中 | 阶段同步 |
| **原子操作** | ✓ | ✓ | 低 | 极高 | 简单操作 |

## 线程同步机制

### 1. 互斥锁（Mutex）
**作用**：保证同一时刻只有一个线程访问共享资源
**特点**：
- 阻塞式等待
- 支持递归锁
- 可能导致死锁

### 2. 读写锁（RWLock）
**作用**：允许多读者同时访问，写者独占
**优势**：读多写少场景性能优异
**劣势**：写者可能饥饿

### 3. 条件变量（Condition Variable）
**作用**：实现复杂的等待-通知机制
**使用**：必须配合互斥锁使用
**应用**：生产者-消费者模型

### 4. 信号量（Semaphore）
**作用**：控制同时访问资源的线程数量
**操作**：P操作（wait）、V操作（signal）
**应用**：资源池管理、限流

### 5. 自旋锁（Spinlock）
**作用**：忙等待获取锁
**适用**：锁持有时间极短（微秒级）
**特点**：无上下文切换，消耗CPU

### 6. 屏障（Barrier）
**作用**：同步多个线程到达某个执行点
**应用**：并行算法中的阶段同步
**特点**：所有线程到达后才能继续

## 进程同步机制

### 1. 跨进程互斥锁
**实现**：
- POSIX：`pthread_mutexattr_setpshared`
- Windows：`CreateMutex`
**特点**：需要共享内存支持

### 2. 进程信号量
**实现**：
- POSIX：`sem_open`、`sem_wait`、`sem_post`
- System V：`semget`、`semop`
**优势**：内核级别支持，可靠性高

### 3. 文件锁
**类型**：
- 建议性锁：`flock`
- 强制性锁：`fcntl`
**特点**：基于文件系统，跨进程可见

### 4. 共享内存 + 同步原语
**组合**：共享内存 + 信号量/互斥锁
**优势**：高性能数据共享
**劣势**：编程复杂度高

## 性能对比分析

### 效率排序（从高到低）
1. **原子操作**：硬件级别，无锁
2. **自旋锁**：无上下文切换（短时间）
3. **互斥锁**：内核优化，适中开销
4. **读写锁**：读多写少场景优异
5. **信号量**：灵活但有一定开销
6. **条件变量**：复杂同步，开销较大

### 选择原则

#### 根据锁持有时间
- **极短（纳秒-微秒）**：自旋锁、原子操作
- **短（微秒-毫秒）**：互斥锁
- **长（毫秒以上）**：互斥锁、信号量

#### 根据访问模式
- **互斥访问**：互斥锁
- **读多写少**：读写锁
- **资源计数**：信号量
- **复杂条件**：条件变量

#### 根据并发级别
- **低并发**：互斥锁
- **高并发**：自旋锁、原子操作
- **读密集**：读写锁

## 面试高频问题

### Q1: 为什么条件变量要配合互斥锁？
1. **保护共享状态**：条件检查需要原子性
2. **避免虚假唤醒**：重新检查条件
3. **防止竞态条件**：signal和wait之间的竞争

### Q2: 自旋锁什么时候效率最高？
**最佳场景**：
- 锁持有时间极短（微秒级）
- 多核CPU系统
- 中断处理程序
- 内核代码

**不适用场景**：
- 单核系统
- 锁持有时间长
- 用户态程序（一般情况）

### Q3: 读写锁的写者饥饿问题如何解决？
1. **写者优先策略**：有写者等待时，阻止新的读者
2. **公平读写锁**：按到达顺序分配锁
3. **超时机制**：设置写者等待超时
4. **写者提升**：长时间等待的写者提升优先级

### Q4: 进程间同步为什么比线程间复杂？
1. **地址空间隔离**：需要共享内存或IPC
2. **内核支持**：需要系统调用参与
3. **生命周期管理**：进程崩溃影响同步状态
4. **性能开销**：跨进程通信成本高

### Q5: 如何选择合适的同步机制？
1. **分析访问模式**：互斥、读写、计数
2. **评估性能要求**：延迟、吞吐量
3. **考虑实现复杂度**：开发和维护成本
4. **测试验证**：实际场景性能测试