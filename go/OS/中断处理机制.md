# 中断处理机制

## 中断基础概念

### 定义
**中断（Interrupt）**：硬件或软件向CPU发出的信号，要求CPU暂停当前执行的程序，转去处理特定事件。

### 中断的作用
1. **提高CPU利用率**：CPU无需轮询等待事件
2. **实现多任务**：支持任务切换和调度
3. **处理异步事件**：响应外部设备请求
4. **异常处理**：处理程序错误和系统异常

## 中断分类

### 按来源分类
| 类型 | 来源 | 示例 | 特点 |
|------|------|------|------|
| **硬件中断** | 外部设备 | 键盘、网卡、定时器 | 异步、不可预测 |
| **软件中断** | 程序指令 | 系统调用、异常 | 同步、可预测 |

### 按优先级分类
- **不可屏蔽中断（NMI）**：最高优先级，不能被禁止
- **可屏蔽中断**：可以通过设置中断标志位禁止
- **异常**：程序执行错误引起的中断

### 按处理方式分类
- **精确中断**：中断点精确，易于恢复
- **不精确中断**：中断点不精确，恢复复杂

## 中断处理流程

### 1. 中断发生
```
外设 → 中断控制器 → CPU
```

### 2. 中断识别
1. **检查中断标志**：CPU检查中断请求
2. **优先级判断**：确定是否响应中断
3. **保存现场**：保存当前程序状态

### 3. 中断响应
```
保存现场 → 查找中断向量 → 跳转到中断处理程序
```

### 4. 中断处理
1. **保存寄存器**：保存更多上下文信息
2. **执行中断服务程序**：处理具体中断事件
3. **恢复寄存器**：恢复上下文信息

### 5. 中断返回
```
恢复现场 → 继续执行被中断的程序
```

## 中断向量表

### 概念
**中断向量表**：存储中断服务程序入口地址的表格

### 结构
```
中断号 → 中断向量 → 中断服务程序地址
```

### x86中断向量表
| 中断号 | 类型 | 描述 |
|--------|------|------|
| 0 | 除零异常 | 除法错误 |
| 1 | 调试异常 | 单步调试 |
| 2 | NMI | 不可屏蔽中断 |
| 3 | 断点异常 | 调试断点 |
| 8 | 双重故障 | 异常处理中的异常 |
| 14 | 页故障 | 内存访问错误 |
| 32-255 | 用户定义 | 硬件设备中断 |

## 中断控制器

### 8259A PIC (可编程中断控制器)
**特点**：
- 管理8个中断源
- 可级联扩展到15个
- 优先级可编程

### APIC (高级可编程中断控制器)
**组成**：
- **Local APIC**：每个CPU核心一个
- **I/O APIC**：管理外部设备中断

**优势**：
- 支持多核处理器
- 更多中断源
- 更灵活的中断分发

## 中断处理优化

### 1. 中断嵌套
**概念**：高优先级中断可以打断低优先级中断处理
**优势**：提高系统响应性
**风险**：栈溢出、死锁

### 2. 中断延迟处理
**上半部（Top Half）**：
- 紧急处理
- 关中断执行
- 尽快完成

**下半部（Bottom Half）**：
- 延迟处理
- 开中断执行
- 复杂逻辑处理

### 3. 中断合并
**技术**：将多个中断合并为一个
**优势**：减少中断处理开销
**应用**：网络数据包处理

## Linux中断处理

### 中断处理机制
1. **硬中断（Hard IRQ）**：原子上下文，不可睡眠
2. **软中断（Soft IRQ）**：可延迟执行
3. **工作队列（Work Queue）**：可睡眠的延迟处理

### 中断处理函数
```c
irqreturn_t interrupt_handler(int irq, void *dev_id) {
    // 快速处理
    // 返回IRQ_HANDLED或IRQ_NONE
}
```

### 中断注册
```c
int request_irq(unsigned int irq, 
                irq_handler_t handler,
                unsigned long flags,
                const char *name,
                void *dev);
```

## 中断性能优化

### 1. 中断亲和性
**概念**：将中断绑定到特定CPU核心
**优势**：
- 提高缓存命中率
- 减少核心间通信
- 负载均衡

```bash
# 设置中断亲和性
echo 2 > /proc/irq/24/smp_affinity
```

### 2. 中断聚合
**技术**：
- **NAPI**：网络设备中断聚合
- **中断合并**：定时器合并多个中断

### 3. 用户态驱动
**技术**：UIO、VFIO
**优势**：
- 减少内核态/用户态切换
- 降低中断处理延迟
- 提高性能

## 面试高频问题

### Q1: 中断和轮询的区别？
**中断方式**：
- 事件驱动，CPU被动响应
- 高效，CPU利用率高
- 实时性好
- 实现复杂

**轮询方式**：
- CPU主动查询
- 简单易实现
- 可能浪费CPU资源
- 实时性差

### Q2: 硬中断和软中断的区别？
**硬中断**：
- 硬件产生
- 优先级高
- 不可嵌套（通常）
- 处理时间短

**软中断**：
- 软件产生
- 优先级低
- 可延迟处理
- 可以睡眠

### Q3: 中断处理的上半部和下半部？
**上半部（Top Half）**：
- 紧急处理，关中断执行
- 保存硬件状态
- 快速完成，不能睡眠

**下半部（Bottom Half）**：
- 延迟处理，开中断执行
- 复杂逻辑处理
- 可以睡眠，可被抢占

### Q4: 如何优化中断处理性能？
1. **减少中断频率**：中断合并、批处理
2. **中断亲和性**：绑定到特定CPU核心
3. **NAPI机制**：网络中断聚合
4. **用户态驱动**：减少内核开销
5. **中断延迟处理**：分离紧急和非紧急处理

### Q5: 中断嵌套的优缺点？
**优点**：
- 提高系统响应性
- 高优先级事件及时处理
- 提高系统吞吐量

**缺点**：
- 增加系统复杂性
- 可能导致栈溢出
- 中断处理时间不可预测
- 可能出现优先级反转
