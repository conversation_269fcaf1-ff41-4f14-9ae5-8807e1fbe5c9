# Linux文件权限详解

## 权限表示方法

### 符号表示法
```
-rwxr-xr--
│└─┬─┘└─┬─┘└─┬─┘
│  │   │   └── 其他用户权限 (others)
│  │   └────── 组权限 (group)
│  └────────── 所有者权限 (user/owner)
└─────────────── 文件类型
```

### 数字表示法
| 权限 | 数值 | 含义 |
|------|------|------|
| **r** | 4 | 读权限 |
| **w** | 2 | 写权限 |
| **x** | 1 | 执行权限 |

**计算方法**：rwx = 4+2+1 = 7

## 基本权限详解

### 对文件的权限
- **r（读）**：查看文件内容
- **w（写）**：修改文件内容
- **x（执行）**：执行文件（脚本、程序）

### 对目录的权限
- **r（读）**：列出目录内容（ls）
- **w（写）**：创建、删除目录中的文件
- **x（执行）**：进入目录（cd）

## 常用权限组合

| 数字 | 符号 | 含义 | 适用场景 |
|------|------|------|----------|
| **755** | rwxr-xr-x | 所有者全权限，其他只读执行 | 可执行文件、目录 |
| **644** | rw-r--r-- | 所有者读写，其他只读 | 普通文件 |
| **600** | rw------- | 仅所有者读写 | 私密文件 |
| **777** | rwxrwxrwx | 所有人全权限 | 临时目录（不推荐） |

## 权限操作命令

### 查看权限
```bash
ls -l filename          # 查看文件权限
ls -ld dirname          # 查看目录权限
stat filename           # 详细权限信息
```

### 修改权限 - 数字方式
```bash
chmod 755 filename       # 设置为rwxr-xr-x
chmod 644 filename       # 设置为rw-r--r--
chmod -R 755 dirname     # 递归设置目录权限
```

### 修改权限 - 符号方式
```bash
chmod u+x filename       # 所有者添加执行权限
chmod g-w filename       # 组去除写权限
chmod o=r filename       # 其他用户设置为只读
chmod a+r filename       # 所有人添加读权限
```

## 特殊权限

### 1. SetUID (SUID) - 4xxx
**作用**：执行文件时以文件所有者身份运行
**标识**：所有者执行位显示为`s`
**示例**：`passwd`命令（普通用户可修改密码）
```bash
chmod 4755 filename      # 设置SUID
chmod u+s filename       # 符号方式设置SUID
```

### 2. SetGID (SGID) - 2xxx
**对文件**：执行时以文件所属组身份运行
**对目录**：新建文件继承目录的组
**标识**：组执行位显示为`s`
```bash
chmod 2755 dirname       # 设置SGID
chmod g+s dirname        # 符号方式设置SGID
```

### 3. Sticky Bit - 1xxx
**作用**：目录中只有文件所有者才能删除自己的文件
**标识**：其他用户执行位显示为`t`
**典型应用**：/tmp目录
```bash
chmod 1755 dirname       # 设置Sticky Bit
chmod +t dirname         # 符号方式设置
```

## 权限检查流程

1. **检查是否为文件所有者**：是 → 使用所有者权限
2. **检查是否为文件所属组成员**：是 → 使用组权限
3. **都不是**：使用其他用户权限

## 面试高频问题

### Q1: 755和644权限的区别？
- **755 (rwxr-xr-x)**：可执行文件权限，所有者可读写执行，其他人可读执行
- **644 (rw-r--r--)**：普通文件权限，所有者可读写，其他人只读

### Q2: 目录权限和文件权限的区别？
**目录权限**：
- r：列出目录内容
- w：创建/删除文件
- x：进入目录

**文件权限**：
- r：读取文件内容
- w：修改文件内容
- x：执行文件

### Q3: SUID的安全风险？
1. **权限提升**：普通用户获得root权限
2. **安全漏洞**：恶意利用SUID程序
3. **最小权限原则**：只在必要时使用SUID

### Q4: 如何批量修改权限？
```bash
# 递归修改目录权限
chmod -R 755 /path/to/dir

# 查找并修改特定文件权限
find /path -name "*.sh" -exec chmod +x {} \;

# 设置默认权限
umask 022  # 新建文件644，目录755
```

### Q5: 权限不够时如何排查？
1. **检查文件权限**：`ls -l filename`
2. **检查目录权限**：`ls -ld dirname`
3. **检查用户组**：`groups username`
4. **检查进程权限**：`ps aux | grep process`
5. **使用sudo**：临时提升权限