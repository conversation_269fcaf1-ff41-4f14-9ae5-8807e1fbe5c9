# Linux信号处理机制

## 信号基础概念

### 1. 什么是信号？
信号是Linux系统中进程间通信的一种机制，用于通知进程发生了某种事件。信号是异步的，可以在任何时候发送给进程。

### 2. 信号的特点
- **异步性**：信号可以在任何时候到达
- **简单性**：信号只能传递很少的信息
- **不可靠性**：信号可能丢失（传统信号）
- **不可排队**：同类型信号不会排队

### 3. 信号分类

#### 标准信号（1-31）
| 信号 | 编号 | 默认动作 | 说明 |
|------|------|----------|------|
| SIGHUP | 1 | 终止 | 挂起信号 |
| SIGINT | 2 | 终止 | 中断信号(Ctrl+C) |
| SIGQUIT | 3 | 终止+core | 退出信号(Ctrl+\) |
| SIGKILL | 9 | 终止 | 强制终止(不可捕获) |
| SIGSEGV | 11 | 终止+core | 段错误 |
| SIGPIPE | 13 | 终止 | 管道破裂 |
| SIGALRM | 14 | 终止 | 定时器信号 |
| SIGTERM | 15 | 终止 | 终止信号 |
| SIGCHLD | 17 | 忽略 | 子进程状态改变 |
| SIGSTOP | 19 | 停止 | 停止执行(不可捕获) |

#### 实时信号（32-64）
- **范围**：SIGRTMIN(32) 到 SIGRTMAX(64)
- **特点**：可靠、可排队、可携带数据

## 信号处理方式

### 1. 默认处理
- **终止进程**：SIGINT, SIGTERM等
- **忽略信号**：SIGCHLD等
- **终止+core**：SIGQUIT, SIGSEGV等
- **停止进程**：SIGSTOP, SIGTSTP等
- **继续进程**：SIGCONT

### 2. 忽略信号
- **使用SIG_IGN**：signal(SIGINT, SIG_IGN)
- **注意**：SIGKILL和SIGSTOP不能被忽略

### 3. 自定义处理函数

#### signal()函数
- **简单注册**：signal(SIGINT, handler)
- **限制**：功能有限，不够灵活

#### sigaction()函数（推荐）
- **更强大**：可设置更多选项
- **可获取信号信息**：发送者PID、携带数据等
- **可设置信号掩码**：处理期间阻塞其他信号
- **标志位**：SA_RESTART、SA_SIGINFO等

## 信号发送

### 1. kill()系统调用
- **发送给进程**：kill(pid, sig)
- **发送给进程组**：kill(-pgid, sig)
- **检查进程存在**：kill(pid, 0)
- **发送给所有进程**：kill(-1, sig)

### 2. 实时信号发送
- **sigqueue()函数**：可携带数据的信号发送
- **特点**：可靠、有序、携带额外信息

### 3. 定时器信号
- **alarm()函数**：简单定时器，精度为秒
- **setitimer()函数**：高精度定时器，支持微秒级
- **定时器类型**：
  - ITIMER_REAL：真实时间，发送SIGALRM
  - ITIMER_VIRTUAL：用户态时间，发送SIGVTALRM
  - ITIMER_PROF：用户态+内核态时间，发送SIGPROF

## 信号掩码和阻塞

### 1. 信号集操作
- **sigemptyset()**：初始化空信号集
- **sigfillset()**：初始化满信号集
- **sigaddset()**：添加信号到集合
- **sigdelset()**：从集合删除信号
- **sigismember()**：检查信号是否在集合中

### 2. 信号掩码控制
- **sigprocmask()**：设置进程信号掩码
  - SIG_BLOCK：阻塞指定信号
  - SIG_UNBLOCK：解除阻塞
  - SIG_SETMASK：设置新掩码

### 3. 等待信号
- **pause()**：暂停进程直到收到信号
- **sigsuspend()**：原子地设置掩码并等待信号

## 信号安全编程

### 1. 异步信号安全函数
#### 安全函数（可在信号处理器中使用）
- **I/O函数**：write(), read(), open(), close()
- **进程控制**：_exit(), fork(), execve()
- **信号函数**：signal(), sigaction(), sigprocmask()

#### 不安全函数（不可在信号处理器中使用）
- **标准I/O**：printf(), scanf(), fopen()
- **内存管理**：malloc(), free(), realloc()
- **大部分库函数**

### 2. 自管道技巧
- **概念**：将异步信号转换为同步I/O事件
- **方法**：信号处理器写入管道，主循环通过select()监听
- **优势**：避免信号处理器中的复杂操作

## 实际应用场景

### 1. 优雅关闭服务
- **信号**：SIGTERM, SIGINT
- **处理**：设置标志位，完成当前任务后退出
- **清理**：关闭连接、保存数据、释放资源

### 2. 子进程管理
- **信号**：SIGCHLD
- **处理**：使用waitpid()回收僵尸进程
- **注意**：使用WNOHANG避免阻塞

### 3. 定时任务
- **信号**：SIGALRM
- **应用**：超时处理、定期任务

### 4. 热重载配置
- **信号**：SIGUSR1, SIGUSR2
- **应用**：重新加载配置文件

## 面试高频问题

### Q1: SIGKILL和SIGTERM的区别？
- **SIGKILL (9)**：不可捕获、不可忽略，强制终止进程
- **SIGTERM (15)**：可捕获，允许进程优雅退出
- **使用场景**：先发SIGTERM，等待一段时间后发SIGKILL

### Q2: signal()和sigaction()的区别？
**signal()缺点**：
- 行为在不同系统上不一致
- 信号处理器执行期间不阻塞其他信号
- 无法获取信号的详细信息

**sigaction()优势**：
- 行为一致可靠
- 可设置信号掩码
- 可获取信号详细信息（SA_SIGINFO）
- 可设置重启系统调用（SA_RESTART）

### Q3: 如何避免僵尸进程？
1. **捕获SIGCHLD信号**：
```c
signal(SIGCHLD, sigchld_handler);
void sigchld_handler(int sig) {
    while (waitpid(-1, NULL, WNOHANG) > 0);
}
```
2. **使用双重fork**：让孙子进程成为孤儿进程
3. **忽略SIGCHLD**：`signal(SIGCHLD, SIG_IGN)`

### Q4: 信号处理器中能调用哪些函数？
**异步信号安全函数**：
- 系统调用：write(), read(), open(), close()
- 进程控制：_exit(), fork(), execve()
- 信号函数：signal(), sigaction()

**不安全函数**：
- 标准I/O：printf(), scanf()
- 内存管理：malloc(), free()
- 大部分库函数

### Q5: 自管道技巧是什么？
**目的**：将异步信号转换为同步I/O事件
**实现**：
1. 创建管道
2. 信号处理器向管道写入数据
3. 主循环通过select/epoll监听管道
4. 读取管道数据处理信号逻辑

**优势**：避免在信号处理器中执行复杂操作
