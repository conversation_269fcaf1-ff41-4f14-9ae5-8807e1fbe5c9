# 页面置换算法

## 基本概念

### 页面置换
当物理内存不足时，操作系统需要将某些页面从内存中移出到磁盘，为新页面腾出空间。选择哪个页面移出的策略就是页面置换算法。

### 缺页中断
- **定义**：访问的页面不在物理内存中
- **处理**：操作系统将页面从磁盘加载到内存
- **开销**：磁盘I/O操作，成本很高

## 主要页面置换算法

### 1. FIFO（先进先出）

#### 算法原理
- 选择最早进入内存的页面进行置换
- 使用队列维护页面进入顺序
- 实现简单，开销小

#### 优缺点
- **优点**：实现简单，公平
- **缺点**：可能出现Belady异常，性能不佳
- **Belady异常**：增加物理页框数，缺页率反而增加

#### 适用场景
- 对性能要求不高的系统
- 内存访问模式随机的应用

### 2. LRU（最近最少使用）

#### 算法原理
- 选择最长时间未被访问的页面进行置换
- 基于局部性原理：最近使用的页面将来被使用的可能性大
- 需要记录每个页面的访问时间

#### 实现方式
1. **计数器法**：每个页面关联一个计数器
2. **栈法**：维护一个页面访问栈
3. **链表法**：使用双向链表维护访问顺序

#### 优缺点
- **优点**：性能好，符合局部性原理
- **缺点**：实现复杂，开销大
- **时间复杂度**：O(n)查找，O(1)更新

### 3. Clock算法（时钟算法）

#### 算法原理
- LRU的近似实现，也称为最近未使用算法
- 为每个页面设置一个引用位（访问位）
- 使用环形链表，指针像时钟一样移动

#### 工作流程
1. 页面被访问时，设置引用位为1
2. 需要置换时，检查指针指向的页面
3. 引用位为0：选择该页面置换
4. 引用位为1：清零并移动指针，给页面第二次机会

#### 优缺点
- **优点**：近似LRU性能，开销较小
- **缺点**：性能略差于LRU
- **改进**：增强Clock算法考虑修改位

### 4. 最佳置换算法（OPT）

#### 算法原理
- 选择将来最长时间不会被访问的页面
- 理论上的最优算法
- 实际无法实现（无法预知未来）

#### 作用
- 作为其他算法的性能基准
- 理论分析和算法比较的标准

### 5. LFU（最少使用频率）

#### 算法原理
- 选择使用频率最低的页面进行置换
- 为每个页面维护一个使用计数器
- 适用于访问模式相对稳定的场景

#### 优缺点
- **优点**：考虑了页面的历史使用情况
- **缺点**：无法适应程序行为的变化
- **问题**：早期频繁使用的页面难以被置换

## 算法性能比较

### 性能指标
- **缺页率**：缺页次数/总访问次数
- **平均访问时间**：考虑磁盘I/O开销
- **算法开销**：维护数据结构的成本

### 性能排序（一般情况）
1. **OPT**：理论最优
2. **LRU**：实际中性能最好
3. **Clock**：LRU的良好近似
4. **FIFO**：性能较差，可能出现异常

### Belady异常
- **定义**：增加页框数，缺页率反而增加
- **出现算法**：FIFO等非栈式算法
- **不出现算法**：LRU、OPT等栈式算法

## 实际应用

### 操作系统中的应用
- **Linux**：使用LRU近似算法
- **Windows**：使用工作集算法
- **数据库**：缓冲池管理

### 影响因素
- **程序局部性**：时间局部性和空间局部性
- **内存大小**：物理内存越大，缺页率越低
- **页面大小**：影响内部碎片和I/O效率

## 工作集模型

### 基本概念
- **工作集**：进程在某个时间窗口内访问的页面集合
- **工作集大小**：工作集中页面的数量
- **驻留集**：进程在内存中的页面集合

### 抖动现象
- **定义**：系统频繁进行页面置换，CPU利用率下降
- **原因**：进程的工作集大于分配的物理页框数
- **解决**：增加内存或减少并发进程数

## 面试要点

### 核心算法对比

| 算法 | 优点 | 缺点 | 复杂度 |
|------|------|------|--------|
| FIFO | 简单公平 | Belady异常 | O(1) |
| LRU | 性能好 | 开销大 | O(n) |
| Clock | 近似LRU，开销小 | 性能略差 | O(1) |
| OPT | 理论最优 | 无法实现 | - |

### 高频问题
1. **LRU如何实现？**
   - 双向链表+哈希表，O(1)时间复杂度
2. **为什么LRU性能好？**
   - 符合程序的时间局部性原理
3. **什么是Belady异常？**
   - FIFO算法中增加页框反而增加缺页率
4. **如何避免抖动？**
   - 工作集模型，保证驻留集≥工作集
5. **实际系统用什么算法？**
   - 多数使用Clock算法或LRU近似算法

### 实现要点
- **LRU实现**：链表+哈希表
- **Clock算法**：环形链表+引用位
- **性能优化**：减少算法开销，提高命中率
