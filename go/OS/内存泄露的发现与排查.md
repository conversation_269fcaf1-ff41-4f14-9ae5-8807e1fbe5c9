# 内存泄漏的发现与排查

## 内存泄漏的表现

- **内存持续增长**：程序内存使用量不断上升，负载减少后也不下降
- **性能下降**：响应时间变长，系统变慢
- **频繁GC**：垃圾回收频繁且耗时长
- **系统资源耗尽**：最终可能导致OOM

## 检测工具和方法

### 1. pprof工具
```bash
# 启用pprof
import _ "net/http/pprof"

# 分析堆内存
go tool pprof http://localhost:6060/debug/pprof/heap

# 常用命令
(pprof) top          # 查看内存占用最高的函数
(pprof) list func    # 查看具体函数的分配情况
(pprof) web          # 生成可视化图形
```

### 2. 内存分析命令
- `go test -memprofile mem.out` + `go tool pprof mem.out`
- `go test -trace trace.out` + `go tool trace trace.out`

### 3. 运行时监控
- `runtime.MemStats`：获取内存统计信息
- 监控Alloc、TotalAlloc、Sys等关键指标

## 常见泄漏原因

### 1. 长生命周期对象持有短生命周期引用
- **问题**：全局对象持有大量临时对象引用
- **解决**：定期清理、使用弱引用、限制引用数量

### 2. 未关闭的资源
- **问题**：文件、网络连接、数据库连接未关闭
- **解决**：使用defer确保资源释放

### 3. 无限增长的缓存
- **问题**：map、slice持续增长不清理
- **解决**：实现LRU缓存、设置大小限制、定期清理

### 4. Goroutine泄漏
- **问题**：goroutine无法正常退出
- **解决**：使用context控制生命周期、避免无限循环

## 排查步骤

1. **监控内存趋势**：观察内存使用是否持续增长
2. **使用pprof分析**：找出内存占用最多的函数
3. **检查代码逻辑**：重点检查资源管理和生命周期
4. **压力测试**：模拟高负载场景验证修复效果

## 预防措施

1. **资源管理**：使用defer确保资源释放
2. **限制缓存大小**：实现LRU或定期清理机制
3. **控制goroutine生命周期**：使用context管理
4. **定期代码审查**：关注内存分配和释放逻辑
5. **监控告警**：设置内存使用阈值告警

## 面试要点

### Q1: Go中为什么会有内存泄漏？
虽然Go有GC，但以下情况仍可能泄漏：
- 长生命周期对象持有短生命周期引用
- 未关闭的资源（文件、连接等）
- 无限增长的缓存或切片
- 无法退出的goroutine

### Q2: 如何检测内存泄漏？
1. **pprof工具**：分析堆内存分配
2. **runtime.MemStats**：监控内存统计
3. **压力测试**：观察内存增长趋势
4. **生产监控**：设置内存告警

### Q3: 内存泄漏的解决思路？
1. **定位问题**：使用pprof找到泄漏点
2. **分析原因**：检查对象引用关系
3. **修复代码**：确保资源正确释放
4. **验证效果**：压力测试验证修复