# 进程间通信方式（IPC）

## IPC方式对比

| 方式 | 速度 | 数据量 | 同步需求 | 适用场景 |
|------|------|--------|----------|----------|
| **共享内存** | 最快 | 大 | 需要 | 高性能数据交换 |
| **消息队列** | 中等 | 中等 | 内置 | 任务分发、解耦 |
| **管道** | 中等 | 小 | 无 | 父子进程通信 |
| **套接字** | 较慢 | 任意 | 无 | 网络/本地通信 |
| **信号** | 快 | 无数据 | 无 | 事件通知 |
| **信号量** | - | 无数据 | 同步工具 | 资源控制 |

## 详细介绍

### 1. 共享内存（Shared Memory）
**原理**：多个进程映射同一块物理内存
**优势**：
- 速度最快，无数据拷贝
- 适合大数据量传输
**劣势**：
- 需要额外同步机制（信号量、互斥锁）
- 编程复杂度高

### 2. 消息队列（Message Queue）
**原理**：内核维护的消息链表
**优势**：
- 内置同步机制
- 支持消息类型和优先级
- 进程解耦
**劣势**：
- 有数据拷贝开销
- 消息大小有限制

### 3. 管道（Pipe）
**匿名管道**：
- 用于父子进程
- 单向通信
- 简单易用

**命名管道（FIFO）**：
- 用于无关进程
- 通过文件系统访问
- 支持双向通信

### 4. 套接字（Socket）
**本地套接字**：同主机进程通信
**网络套接字**：跨主机通信
**类型**：TCP（可靠）、UDP（快速）

### 5. 信号（Signal）
**特点**：
- 异步通知机制
- 轻量级，无数据传输
- 用于进程控制和异常处理

### 6. 信号量（Semaphore）
**作用**：控制共享资源访问
**操作**：P操作（wait）、V操作（signal）
**应用**：配合共享内存实现同步

## 选择策略

### 根据数据量选择
- **大数据量**：共享内存
- **中等数据量**：消息队列
- **小数据量**：管道
- **无数据传输**：信号

### 根据关系选择
- **父子进程**：匿名管道
- **无关进程**：命名管道、消息队列、共享内存
- **跨网络**：套接字

### 根据性能要求选择
- **高性能**：共享内存 + 信号量
- **简单可靠**：消息队列
- **轻量通知**：信号

## 面试高频问题

### Q1: 共享内存为什么最快？
1. **零拷贝**：数据直接在内存中共享，无需拷贝
2. **无系统调用**：读写操作直接访问内存
3. **无内核参与**：避免用户态/内核态切换

### Q2: 共享内存的同步问题如何解决？
1. **信号量**：控制访问权限
2. **互斥锁**：保证互斥访问
3. **条件变量**：实现复杂同步逻辑
4. **内存屏障**：保证内存访问顺序

### Q3: 消息队列vs共享内存？
**消息队列优势**：
- 内置同步，使用简单
- 消息边界清晰
- 进程解耦度高

**共享内存优势**：
- 性能最高
- 适合大数据量
- 灵活性强

### Q4: 管道的局限性？
1. **单向通信**：匿名管道只能单向
2. **缓冲区限制**：有大小限制
3. **进程关系**：匿名管道需要亲缘关系
4. **数据格式**：字节流，无消息边界

### Q5: 如何选择IPC方式？
1. **分析数据量**：大数据选共享内存
2. **考虑同步复杂度**：简单场景选消息队列
3. **评估性能要求**：高性能选共享内存
4. **考虑进程关系**：父子进程可选管道
5. **跨网络需求**：选择套接字