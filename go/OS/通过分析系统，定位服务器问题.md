# 服务器性能问题定位与排查

## 问题分析思路

服务器性能问题通常表现为：负载高、响应慢、资源不足。需要从CPU、内存、I/O、网络四个维度进行分析。

## 系统监控工具

### 1. CPU分析 - `top`/`htop`
**关键指标**：
- **us**：用户CPU时间（高则应用逻辑复杂）
- **sy**：系统CPU时间（高则系统调用频繁）
- **wa**：等待I/O的CPU时间（高则I/O瓶颈）
- **load average**：1/5/15分钟负载平均值

### 2. 内存分析 - `free`/`vmstat`
**关键指标**：
- **Available**：可用内存
- **Swap**：交换分区使用（过高表示内存不足）
- **buffer/cache**：缓冲区和缓存使用
- **si/so**：swap in/out（>0表示内存压力）

### 3. I/O分析 - `iostat`/`iotop`
**关键指标**：
- **%util**：设备利用率（>80%表示I/O瓶颈）
- **await**：平均等待时间
- **r/s, w/s**：每秒读写次数
- **rkB/s, wkB/s**：每秒读写数据量

### 4. 网络分析 - `nethogs`/`ss`
**关键指标**：
- **带宽使用**：进程网络流量
- **连接状态**：TIME_WAIT过多表示连接管理问题
- **网络错误**：丢包、重传等

## 排查步骤

### 第一步：整体负载评估
1. `top` - 查看CPU、内存、负载
2. `free -h` - 查看内存使用详情
3. `df -h` - 查看磁盘空间

### 第二步：定位瓶颈资源
1. **CPU瓶颈**：`top`中CPU使用率高
2. **内存瓶颈**：swap使用高，available内存低
3. **I/O瓶颈**：`iostat`中%util高，wa高
4. **网络瓶颈**：`nethogs`显示高流量

### 第三步：进程级分析
1. `ps aux` - 查看进程资源使用
2. `lsof -p PID` - 查看进程打开的文件
3. `strace -p PID` - 跟踪系统调用
4. `pstack PID` - 查看进程调用栈

### 第四步：日志分析
1. **系统日志**：`/var/log/messages`, `/var/log/syslog`
2. **应用日志**：查看错误和异常
3. **内核日志**：`dmesg`查看内核消息

## 常见问题及解决

### CPU问题
- **高用户CPU**：代码优化、算法改进
- **高系统CPU**：减少系统调用、I/O优化
- **上下文切换多**：减少线程数、优化锁

### 内存问题
- **内存泄漏**：使用valgrind、pprof检测
- **内存不足**：增加内存、优化缓存
- **频繁swap**：调整swappiness参数

### I/O问题
- **磁盘I/O高**：使用SSD、优化文件系统
- **网络I/O高**：优化网络配置、使用CDN

## 面试要点

### Q1: 服务器响应慢如何排查？
1. **top**查看整体负载
2. **free**查看内存使用
3. **iostat**查看I/O状况
4. **netstat**查看网络连接
5. 分析日志找出异常

### Q2: 如何定位CPU使用率高的原因？
1. **top**找出CPU占用高的进程
2. **strace**跟踪系统调用
3. **perf**进行性能分析
4. 检查代码逻辑和算法

### Q3: 内存使用率高怎么处理？
1. 检查是否有内存泄漏
2. 分析缓存使用是否合理
3. 考虑增加物理内存
4. 优化应用内存使用