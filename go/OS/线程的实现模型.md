# 线程的实现模型

## 概述
线程实现模型主要分为三种：用户级线程、内核级线程和混合模型。每种模型在性能、并发性和复杂度上各有特点。

## 1. 用户级线程模型（User-Level Threads）

### 基本概念
- 完全在用户空间实现，内核不感知线程存在
- 线程管理由用户态线程库负责
- 内核只看到单个进程

### 特点
- **优点**：
  - 创建和切换开销小
  - 可自定义调度算法
  - 无需系统调用
- **缺点**：
  - 无法利用多核CPU
  - 一个线程阻塞导致整个进程阻塞
  - 无法实现真正的并行

## 2. 内核级线程模型（Kernel-Level Threads）

### 基本概念
- 由操作系统内核直接管理
- 每个线程都有内核调度实体
- 内核负责线程的创建、调度和销毁

### 特点
- **优点**：
  - 充分利用多核CPU
  - 线程独立调度，一个阻塞不影响其他
  - 真正的并行执行
- **缺点**：
  - 创建和切换开销大
  - 需要用户态/内核态切换
  - 消耗更多系统资源

## 3. 混合线程模型（M:N模型）

### 基本概念
- 结合用户级和内核级线程优点
- M个用户级线程映射到N个内核级线程
- 用户态调度器负责用户线程调度

### 特点
- **优点**：
  - 兼具高性能和多核利用
  - 灵活的调度策略
  - 减少系统调用开销
- **缺点**：
  - 实现复杂度高
  - 需要复杂的映射管理
  - 调试困难

## 模型对比

| 特性 | 用户级线程 | 内核级线程 | 混合模型 |
|------|------------|------------|----------|
| 创建开销 | 低 | 高 | 中等 |
| 切换开销 | 低 | 高 | 中等 |
| 多核利用 | 否 | 是 | 是 |
| 阻塞影响 | 全部阻塞 | 独立 | 部分阻塞 |
| 实现复杂度 | 低 | 中等 | 高 |

## 面试要点

1. **核心区别**：调度位置（用户态vs内核态）
2. **性能权衡**：开销vs并发能力
3. **适用场景**：根据应用特点选择
4. **实际应用**：Go语言采用M:N模型