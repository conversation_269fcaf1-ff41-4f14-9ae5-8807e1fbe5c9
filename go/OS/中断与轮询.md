# 中断与轮询

## 基本概念

### 中断（Interrupt）
**中断**：硬件设备向CPU发送信号，请求CPU暂停当前工作，转去处理紧急事件的机制。

### 轮询（Polling）
**轮询**：CPU主动、周期性地检查设备状态，判断是否需要处理的机制。

## 中断机制详解

### 中断类型
1. **硬件中断**：外部设备产生（键盘、鼠标、网卡等）
2. **软件中断**：程序执行产生（系统调用、异常）
3. **内部中断**：CPU内部产生（除零错误、页面错误）

### 中断处理流程
1. **中断发生**：设备发送中断信号
2. **中断识别**：CPU识别中断源和类型
3. **保存现场**：保存当前程序状态
4. **中断处理**：执行中断服务程序
5. **恢复现场**：恢复被中断程序的执行

### 中断向量表
- **作用**：存储各种中断的处理程序地址
- **索引**：通过中断号快速找到处理程序
- **位置**：通常位于内存的固定位置

### 中断优先级
- **高优先级**：时钟中断、电源故障
- **中优先级**：磁盘I/O、网络I/O
- **低优先级**：键盘、鼠标输入

### 中断屏蔽
- **可屏蔽中断**：可以被禁用的中断
- **不可屏蔽中断**：不能被禁用的紧急中断
- **中断屏蔽寄存器**：控制中断的启用/禁用

## 轮询机制详解

### 轮询原理
CPU按固定时间间隔检查设备状态寄存器，判断设备是否需要服务。

### 轮询实现
```c
// 简单轮询示例
while (1) {
    if (device_ready()) {
        process_device();
    }
    // 可能加入延时
    sleep(poll_interval);
}
```

### 轮询策略
1. **忙等待**：持续检查，CPU占用高
2. **定时轮询**：定期检查，有延迟
3. **自适应轮询**：根据负载调整频率

## 中断 vs 轮询对比

### 性能对比

| 特性 | 中断 | 轮询 |
|------|------|------|
| **响应时间** | 快（实时响应） | 慢（有轮询间隔） |
| **CPU利用率** | 高（按需处理） | 低（空轮询浪费） |
| **实现复杂度** | 高（需要硬件支持） | 低（软件实现） |
| **系统开销** | 低（事件驱动） | 高（持续检查） |
| **可预测性** | 差（异步） | 好（同步） |

### 适用场景

**中断适用于**：
- 事件发生频率低
- 对响应时间要求高
- 系统资源充足
- 硬件支持完善

**轮询适用于**：
- 事件发生频率高
- 对实时性要求不严格
- 系统简单，硬件支持有限
- 需要可预测的行为

## 混合策略

### 中断+轮询
现代系统常采用混合策略：
1. **低负载时**：使用中断，节省CPU
2. **高负载时**：切换到轮询，避免中断风暴
3. **自适应切换**：根据负载动态调整

### NAPI（New API）
Linux网络子系统的混合策略：
- **低流量**：中断驱动
- **高流量**：轮询模式
- **动态切换**：根据网络负载自动切换

## 中断优化技术

### 1. 中断合并
- **原理**：将多个中断合并为一个
- **优势**：减少中断处理开销
- **应用**：网络设备、存储设备

### 2. 中断亲和性
- **原理**：将中断绑定到特定CPU核心
- **优势**：提高缓存命中率，减少核心间通信
- **配置**：通过/proc/irq/*/smp_affinity设置

### 3. 中断线程化
- **原理**：将中断处理程序运行在内核线程中
- **优势**：提高系统响应性，支持抢占
- **应用**：实时系统

### 4. MSI/MSI-X
- **MSI**：Message Signaled Interrupts
- **优势**：减少中断延迟，支持更多中断
- **应用**：PCIe设备

## 轮询优化技术

### 1. 自适应轮询
- **动态调整**：根据设备活跃度调整轮询频率
- **负载感知**：高负载时增加频率，低负载时降低频率

### 2. 批量处理
- **原理**：一次轮询处理多个事件
- **优势**：提高吞吐量，减少系统调用

### 3. 优先级轮询
- **原理**：按优先级顺序轮询设备
- **优势**：保证重要设备的响应时间

## 实际应用

### 网络I/O
- **传统模式**：中断驱动
- **高性能模式**：轮询（如DPDK）
- **混合模式**：NAPI机制

### 存储I/O
- **机械硬盘**：中断驱动（延迟高）
- **SSD**：轮询或混合（延迟低）
- **NVMe**：多队列+轮询

### 实时系统
- **硬实时**：轮询保证确定性
- **软实时**：中断提供响应性
- **混合系统**：根据任务特性选择

## 面试高频问题

### Q1: 中断和轮询的区别？
**中断**：
- 事件驱动，被动响应
- 响应快，CPU利用率高
- 实现复杂，需要硬件支持

**轮询**：
- 主动检查，周期性查询
- 有延迟，CPU可能空转
- 实现简单，行为可预测

### Q2: 什么时候用中断，什么时候用轮询？
**使用中断**：
- 事件发生频率低
- 对响应时间要求高
- 系统资源充足

**使用轮询**：
- 事件发生频率高
- 对延迟不敏感
- 需要可预测的行为

### Q3: 中断处理的步骤是什么？
1. **中断发生**：硬件发送中断信号
2. **中断识别**：CPU识别中断源
3. **保存现场**：保存寄存器状态
4. **中断处理**：执行中断服务程序
5. **恢复现场**：恢复程序执行

### Q4: 什么是中断风暴？如何解决？
**中断风暴**：中断频率过高，CPU大部分时间处理中断

**解决方案**：
1. **中断合并**：合并多个中断
2. **切换到轮询**：高负载时使用轮询
3. **中断限流**：限制中断频率
4. **负载均衡**：分散中断到多个CPU

### Q5: Linux的NAPI机制是什么？
**NAPI**：New API，Linux网络子系统的混合机制
- **低负载**：中断驱动，节省CPU
- **高负载**：轮询模式，避免中断风暴
- **自动切换**：根据网络流量动态调整

## 性能调优

### 中断调优
1. **中断亲和性**：绑定中断到特定CPU
2. **中断合并**：减少中断频率
3. **中断优先级**：合理设置优先级

### 轮询调优
1. **轮询频率**：平衡延迟和CPU使用
2. **批量处理**：一次处理多个事件
3. **CPU亲和性**：固定轮询线程到特定CPU

### 系统级调优
1. **负载均衡**：分散中断负载
2. **CPU隔离**：为关键任务预留CPU
3. **内存优化**：减少缓存缺失
