# 协程调度原理与实现

## 基础概念

### 用户态调度 vs 内核态调度

| 特性 | 内核态调度(线程) | 用户态调度(协程) |
|------|------------------|------------------|
| 调度器位置 | 操作系统内核 | 用户程序内部 |
| 调度时机 | 时间片、中断、系统调用 | 主动让出、I/O阻塞 |
| 上下文切换 | 用户态↔内核态 | 用户态内部 |
| 切换开销 | 大(完整CPU状态) | 小(必要寄存器) |
```

## Go语言的GPM调度模型

### GPM模型组件

#### G (Goroutine)
- **定义**：协程，用户态轻量级线程
- **包含**：协程栈、调度信息、状态、ID
- **状态**：idle、runnable、running、syscall、waiting、dead

#### P (Processor)
- **定义**：逻辑处理器，调度上下文
- **包含**：本地运行队列、关联的M
- **数量**：通常等于GOMAXPROCS设置值

#### M (Machine)
- **定义**：系统线程，真正的执行单元
- **包含**：当前协程、关联的P、线程ID
- **特点**：按需创建，可复用

### 调度流程
1. **创建协程**：go关键字创建G，加入P的本地队列
2. **M获取G**：M从P的本地队列获取G执行
3. **执行协程**：M执行G的代码
4. **调度时机**：主动让出、阻塞、抢占时触发调度
5. **工作窃取**：P的队列为空时从其他P窃取G

## 协程调度策略

### 1. 工作窃取（Work Stealing）
- **原理**：空闲P从其他P的本地队列窃取G
- **优点**：负载均衡，提高CPU利用率
- **实现**：从队列尾部窃取，减少竞争

### 2. 抢占式调度
- **原理**：防止协程长时间占用CPU
- **触发**：函数调用、系统调用、垃圾回收
- **机制**：基于信号的异步抢占

## 协程状态转换

### 状态流转
- **idle** → **runnable**：协程创建后进入可运行队列
- **runnable** → **running**：被调度器选中执行
- **running** → **waiting**：阻塞在channel、锁等操作
- **waiting** → **runnable**：阻塞条件解除
- **running** → **dead**：协程执行完毕

## 协程栈管理

### 栈特性
- **初始大小**：2KB起始栈
- **动态增长**：按需分配，最大可达1GB
- **栈收缩**：不使用时自动释放
- **栈拷贝**：增长时可能需要拷贝整个栈

## 调度器优化

### 关键优化技术
1. **本地队列**：减少锁竞争，提高缓存局部性
2. **工作窃取**：负载均衡，避免空闲
3. **系统调用优化**：M与P分离，避免阻塞其他协程
4. **抢占调度**：防止协程饿死

## 面试要点

### 1. GPM调度模型
- **G**：协程，轻量级用户态线程
- **P**：处理器，调度上下文，数量=GOMAXPROCS
- **M**：系统线程，真正的执行单元

### 2. 调度时机
- 主动让出：runtime.Gosched()
- 系统调用：阻塞时M与P分离
- Channel操作：发送/接收阻塞
- 抢占调度：防止长时间占用CPU

### 3. 关键特性
- **工作窃取**：负载均衡机制
- **本地队列**：减少锁竞争
- **栈管理**：2KB起始，动态增长
- **协程泄漏**：使用context、WaitGroup避免
