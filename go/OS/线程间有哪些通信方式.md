# 线程间通信与同步

## 通信方式对比

| 方式 | 效率 | 复杂度 | 适用场景 | 特点 |
|------|------|--------|----------|------|
| **共享内存** | 最高 | 高 | 高频数据交换 | 需要同步保护 |
| **消息队列** | 中等 | 中等 | 任务分发 | 解耦性好 |
| **条件变量** | 高 | 高 | 复杂同步 | 灵活性强 |
| **信号量** | 高 | 低 | 资源控制 | 简单有效 |
| **Channel** | 中等 | 低 | Go语言 | 类型安全 |

## 主要通信方式

### 1. 共享内存
**原理**：线程共享进程地址空间，直接访问共享变量
**优势**：
- 效率最高，无数据拷贝
- 直接内存访问
**劣势**：
- 需要同步机制保护
- 容易出现竞态条件
**同步工具**：互斥锁、读写锁、原子操作

### 2. 消息队列
**原理**：线程间通过队列传递消息
**优势**：
- 线程解耦
- 天然的同步机制
- 支持异步通信
**劣势**：
- 有数据拷贝开销
- 内存占用较大

### 3. 条件变量（Condition Variable）
**原理**：线程等待特定条件，条件满足时被唤醒
**使用模式**：
```
// 等待方
lock(mutex)
while (!condition) {
    wait(cond, mutex)
}
// 处理逻辑
unlock(mutex)

// 通知方
lock(mutex)
// 改变条件
condition = true
signal(cond)
unlock(mutex)
```
**应用**：生产者-消费者模型

### 4. 信号量（Semaphore）
**原理**：计数器控制资源访问
**操作**：
- P操作（wait）：计数减1，为0时阻塞
- V操作（signal）：计数加1，唤醒等待线程
**应用**：控制有限资源访问

## 同步机制

### 1. 互斥锁（Mutex）
**作用**：保证同一时刻只有一个线程访问共享资源
**特点**：
- 阻塞式等待
- 支持递归锁
- 有死锁风险

### 2. 读写锁（RWLock）
**作用**：允许多个读者同时访问，写者独占
**优势**：提高读多写少场景的并发性
**劣势**：写者可能饥饿

### 3. 自旋锁（Spinlock）
**作用**：忙等待获取锁
**适用**：锁持有时间极短的场景
**特点**：
- 无上下文切换开销
- 消耗CPU资源
- 不适合单核系统

### 4. 原子操作
**作用**：不可分割的操作，无需额外同步
**类型**：
- 原子读写
- 原子算术运算
- 原子比较交换（CAS）

## 现代语言特性

### Go语言Channel
**特点**：
- 类型安全的消息传递
- 支持缓冲和无缓冲
- 实现CSP并发模型
**使用**：`go func() { ch <- data }()`

### C++11并发支持
- `std::mutex`：互斥锁
- `std::condition_variable`：条件变量
- `std::atomic`：原子操作
- `std::future`：异步结果

## 面试高频问题

### Q1: 共享内存vs消息传递？
**共享内存优势**：
- 效率高，无数据拷贝
- 适合大数据量交换
**消息传递优势**：
- 线程解耦，安全性好
- 天然同步，编程简单

### Q2: 如何避免死锁？
1. **锁排序**：总是按相同顺序获取锁
2. **超时机制**：设置获取锁的超时时间
3. **避免嵌套锁**：减少同时持有多个锁
4. **使用无锁数据结构**：原子操作和CAS

### Q3: 条件变量为什么要配合互斥锁？
1. **保护共享状态**：条件检查需要原子性
2. **避免虚假唤醒**：重新检查条件
3. **防止竞态条件**：signal和wait之间的竞争

### Q4: 自旋锁vs互斥锁？
**自旋锁适用**：
- 锁持有时间极短（微秒级）
- 多核系统
- 中断处理程序

**互斥锁适用**：
- 锁持有时间较长
- 单核系统
- 用户态程序

### Q5: 原子操作的优势？
1. **无锁编程**：避免锁的开销
2. **高性能**：硬件级别支持
3. **无死锁**：不存在锁竞争
4. **可扩展性**：适合高并发场景