# Linux零拷贝技术

## 核心概念

**零拷贝（Zero Copy）**：减少数据在内核空间和用户空间之间的复制操作，提高I/O效率的优化技术。

### 传统I/O的问题
```
磁盘 → 内核缓冲区 → 用户缓冲区 → 内核缓冲区 → 网卡
      ①复制      ②复制      ③复制
```
**问题**：数据被复制3次，涉及4次上下文切换

### 零拷贝的优势
- **减少CPU消耗**：避免不必要的数据复制
- **降低内存带宽占用**：减少内存访问次数
- **减少上下文切换**：减少用户态/内核态切换
- **提高传输效率**：利用DMA直接传输

## 零拷贝技术实现

### 1. sendfile()
**原理**：直接在内核空间完成文件到套接字的数据传输
```c
ssize_t sendfile(int out_fd, int in_fd, off_t *offset, size_t count);
```

**流程**：
```
磁盘 → 内核缓冲区 → 网卡
      ①DMA读取   ②DMA发送
```

**特点**：
- 只有2次复制（都是DMA）
- 数据不经过用户空间
- 适用于文件到网络的传输

### 2. mmap() + write()
**原理**：将文件映射到内存，减少一次复制
```c
void *mmap(void *addr, size_t length, int prot, int flags, int fd, off_t offset);
```

**流程**：
```
磁盘 → 内核缓冲区 ←→ 用户空间(映射) → 网卡
      ①DMA读取    ②虚拟映射      ③复制
```

**特点**：
- 减少一次复制
- 文件内容映射到用户空间
- 适用于需要处理数据的场景

### 3. splice()
**原理**：在两个文件描述符间移动数据，不经过用户空间
```c
ssize_t splice(int fd_in, loff_t *off_in, int fd_out, loff_t *off_out,
               size_t len, unsigned int flags);
```

**特点**：
- 完全在内核空间操作
- 支持管道、套接字、文件
- 可以实现真正的零拷贝

### 4. tee()
**原理**：在两个管道间复制数据，不消耗数据
```c
ssize_t tee(int fd_in, int fd_out, size_t len, unsigned int flags);
```

**用途**：
- 数据分流
- 管道数据复制
- 调试和监控

## 性能对比

| 技术 | 复制次数 | 上下文切换 | CPU占用 | 适用场景 |
|------|----------|------------|---------|----------|
| **传统read/write** | 4次 | 4次 | 高 | 通用 |
| **mmap + write** | 3次 | 4次 | 中 | 需要数据处理 |
| **sendfile** | 2次 | 2次 | 低 | 文件传输 |
| **splice** | 0次 | 2次 | 最低 | 管道/套接字 |

## 应用场景

### 1. Web服务器
- **静态文件服务**：使用sendfile()传输文件
- **反向代理**：使用splice()转发数据
- **文件下载**：零拷贝提高下载速度

### 2. 文件传输
- **FTP服务器**：大文件传输优化
- **备份系统**：文件复制优化
- **分布式存储**：数据复制优化

### 3. 流媒体服务
- **视频点播**：大文件流式传输
- **直播服务**：实时数据转发
- **CDN节点**：内容分发优化

## 面试高频问题

### Q1: 零拷贝的核心原理？
**传统I/O问题**：数据在用户空间和内核空间间多次复制
**零拷贝解决**：
1. 减少复制次数
2. 利用DMA直接传输
3. 避免用户空间参与
4. 减少上下文切换

### Q2: sendfile和mmap的区别？
**sendfile**：
- 完全在内核空间操作
- 适用于文件到网络传输
- 无法处理数据内容
- 性能最优

**mmap**：
- 文件映射到用户空间
- 可以处理数据内容
- 仍有一次复制
- 灵活性更高

### Q3: 零拷贝的局限性？
1. **数据处理限制**：无法在传输过程中修改数据
2. **文件大小限制**：某些实现对文件大小有限制
3. **平台依赖**：不同系统实现可能不同
4. **应用场景限制**：主要适用于文件传输场景

### Q4: 如何选择零拷贝技术？
**选择依据**：
- **纯文件传输**：sendfile()
- **需要数据处理**：mmap() + write()
- **管道/套接字间传输**：splice()
- **数据分流**：tee()

### Q5: 零拷贝在Go中的应用？
- **io.Copy()**：底层可能使用sendfile
- **net.TCPConn.ReadFrom()**：支持零拷贝
- **os.File.WriteTo()**：文件到网络的零拷贝
- **syscall包**：直接调用系统调用