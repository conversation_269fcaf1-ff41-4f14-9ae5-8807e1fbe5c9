# 进程和线程的区别

## 基本定义

**进程（Process）**：操作系统分配资源的基本单位，拥有独立的内存空间
**线程（Thread）**：CPU调度的基本单位，共享进程资源的执行单元

## 核心区别对比

| 维度 | 进程 | 线程 |
|------|------|------|
| **内存空间** | 独立的虚拟地址空间 | 共享进程地址空间 |
| **创建开销** | 大（需分配独立内存） | 小（只需分配栈） |
| **切换开销** | 大（切换页表、刷新TLB） | 小（不切换内存映射） |
| **通信方式** | IPC（管道、消息队列、共享内存） | 直接共享内存 |
| **故障隔离** | 崩溃不影响其他进程 | 崩溃影响整个进程 |
| **并发数量** | 受内存限制（百级） | 可创建更多（千级） |

## 内存布局差异

**进程内存**：独立的代码段、数据段、堆、栈
**线程内存**：
- 共享：代码段、数据段、堆、文件描述符
- 独立：栈空间、程序计数器、寄存器

## 使用场景

**选择进程**：
- 需要强隔离性（浏览器标签页）
- 容错性要求高
- 微服务架构

**选择线程**：
- 频繁数据共享
- 高并发处理
- 计算密集型任务

## 面试要点

1. **本质区别**：进程是资源分配单位，线程是调度单位
2. **性能对比**：线程创建、切换、通信都比进程快
3. **安全性**：进程隔离性好，线程共享风险
4. **实际应用**：Web服务器用线程池处理请求，浏览器用进程隔离标签页