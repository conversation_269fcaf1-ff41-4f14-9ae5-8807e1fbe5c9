# Linux 常用命令

## 1. 文件和目录操作

| 命令 | 功能 | 常用选项 |
|------|------|----------|
| ls | 列出目录内容 | -l(详细), -a(隐藏文件) |
| cd | 切换目录 | cd ..(上级), cd ~(家目录) |
| pwd | 显示当前目录 | - |
| mkdir | 创建目录 | -p(递归创建) |
| rm | 删除文件/目录 | -r(递归), -f(强制) |
| cp | 复制文件/目录 | -r(递归), -p(保持属性) |
| mv | 移动/重命名 | - |

## 2. 文件内容查看

| 命令 | 功能 | 常用选项 |
|------|------|----------|
| cat | 查看文件内容 | -n(显示行号) |
| more | 分页查看 | 空格(下一页), q(退出) |
| less | 增强分页查看 | 支持上下滚动 |
| head | 查看文件头部 | -n(指定行数) |
| tail | 查看文件尾部 | -f(实时监控), -n(指定行数) |

## 3. 权限管理

| 命令 | 功能 | 示例 |
|------|------|------|
| chmod | 修改文件权限 | chmod 755 file, chmod +x script |
| chown | 修改所有者 | chown user:group file |
| umask | 设置默认权限 | umask 022 |

## 4. 进程管理

| 命令 | 功能 | 常用选项 |
|------|------|----------|
| ps | 查看进程 | -ef(所有进程), aux(详细信息) |
| top | 实时进程监控 | q(退出), k(杀死进程) |
| kill | 终止进程 | -9(强制), -15(正常) |
| jobs | 查看后台任务 | - |
| bg/fg | 后台/前台切换 | bg %1, fg %1 |

## 5. 网络相关

| 命令 | 功能 | 常用选项 |
|------|------|----------|
| ping | 测试网络连通性 | -c(次数), -t(超时) |
| ifconfig | 网络接口配置(旧) | - |
| ip | 网络接口配置(新) | addr, route, link |
| netstat | 查看网络连接 | -an(所有), -l(监听) |
| curl | HTTP请求工具 | -X(方法), -d(数据) |
| wget | 文件下载 | -O(输出文件) |

## 6. 磁盘管理

| 命令 | 功能 | 常用选项 |
|------|------|----------|
| df | 查看磁盘使用 | -h(人类可读) |
| du | 查看目录大小 | -sh(汇总), -h(可读) |
| mount | 挂载文件系统 | -t(类型) |
| umount | 卸载文件系统 | -f(强制) |

## 7. 系统信息

| 命令 | 功能 | 常用选项 |
|------|------|----------|
| uname | 系统信息 | -a(全部), -r(内核版本) |
| whoami | 当前用户 | - |
| uptime | 系统运行时间 | - |
| free | 内存使用情况 | -h(可读格式) |

## 面试要点

### 高频问题
1. **实时查看日志**：`tail -f logfile`
2. **查找文件**：`find /path -name "*.txt"`
3. **查看端口占用**：`netstat -tulpn | grep :80`
4. **杀死进程**：`ps aux | grep process_name` → `kill -9 PID`
5. **查看系统负载**：`top`, `htop`, `uptime`
6. **磁盘空间**：`df -h`, `du -sh *`

### 组合使用示例
- **查找大文件**：`find / -size +100M -exec ls -lh {} \;`
- **统计文件行数**：`wc -l *.txt`
- **批量重命名**：`rename 's/old/new/' *.txt`