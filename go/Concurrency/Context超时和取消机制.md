# Context超时和取消机制

## Context基础概念

### 什么是Context
Context是Go语言中用于在goroutine之间传递取消信号、超时时间和请求范围值的标准方式。

### 核心接口
```go
type Context interface {
    Deadline() (deadline time.Time, ok bool)
    Done() <-chan struct{}
    Err() error
    Value(key interface{}) interface{}
}
```

## Context类型

### 1. 根Context
```go
// 空Context，永不取消，没有值，没有截止时间
ctx := context.Background()

// TODO Context，当不确定使用哪个Context时的占位符
ctx := context.TODO()
```

### 2. 取消Context
```go
// 创建可取消的Context
ctx, cancel := context.WithCancel(context.Background())

// 手动取消
cancel()

// 检查是否被取消
select {
case <-ctx.Done():
    fmt.Println("Context被取消:", ctx.Err())
default:
    fmt.Println("Context仍然活跃")
}
```

### 3. 超时Context
```go
// 创建带超时的Context
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel() // 避免资源泄漏

// 创建带截止时间的Context
deadline := time.Now().Add(10 * time.Second)
ctx, cancel := context.WithDeadline(context.Background(), deadline)
defer cancel()
```

### 4. 值Context
```go
// 创建带值的Context
ctx := context.WithValue(context.Background(), "userID", 12345)

// 获取值
if userID := ctx.Value("userID"); userID != nil {
    fmt.Println("User ID:", userID.(int))
}
```

## 实际应用场景

### 1. HTTP请求超时控制
```go
func httpRequestWithTimeout() {
    ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
    defer cancel()
    
    req, err := http.NewRequestWithContext(ctx, "GET", "https://api.example.com", nil)
    if err != nil {
        log.Fatal(err)
    }
    
    client := &http.Client{}
    resp, err := client.Do(req)
    if err != nil {
        if ctx.Err() == context.DeadlineExceeded {
            fmt.Println("请求超时")
        } else {
            fmt.Println("请求失败:", err)
        }
        return
    }
    defer resp.Body.Close()
    
    // 处理响应
}
```

### 2. 数据库查询超时
```go
func queryWithTimeout(db *sql.DB) {
    ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
    defer cancel()
    
    rows, err := db.QueryContext(ctx, "SELECT * FROM users WHERE active = ?", true)
    if err != nil {
        if ctx.Err() == context.DeadlineExceeded {
            fmt.Println("数据库查询超时")
        } else {
            fmt.Println("查询失败:", err)
        }
        return
    }
    defer rows.Close()
    
    // 处理结果
}
```

### 3. Goroutine协调取消
```go
func workerWithContext(ctx context.Context, id int) {
    for {
        select {
        case <-ctx.Done():
            fmt.Printf("Worker %d 收到取消信号: %v\n", id, ctx.Err())
            return
        default:
            // 执行工作
            fmt.Printf("Worker %d 正在工作\n", id)
            time.Sleep(time.Second)
        }
    }
}

func coordinatedWorkers() {
    ctx, cancel := context.WithCancel(context.Background())
    
    // 启动多个worker
    for i := 0; i < 3; i++ {
        go workerWithContext(ctx, i)
    }
    
    // 5秒后取消所有worker
    time.Sleep(5 * time.Second)
    cancel()
    
    // 等待一段时间让worker完成清理
    time.Sleep(time.Second)
}
```

### 4. 管道处理超时
```go
func pipelineWithTimeout() {
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    // 第一阶段：数据生成
    input := make(chan int)
    go func() {
        defer close(input)
        for i := 0; i < 100; i++ {
            select {
            case input <- i:
            case <-ctx.Done():
                return
            }
            time.Sleep(100 * time.Millisecond)
        }
    }()
    
    // 第二阶段：数据处理
    output := make(chan int)
    go func() {
        defer close(output)
        for {
            select {
            case data, ok := <-input:
                if !ok {
                    return
                }
                // 处理数据
                processed := data * 2
                select {
                case output <- processed:
                case <-ctx.Done():
                    return
                }
            case <-ctx.Done():
                return
            }
        }
    }()
    
    // 消费处理后的数据
    for {
        select {
        case result, ok := <-output:
            if !ok {
                fmt.Println("管道处理完成")
                return
            }
            fmt.Println("处理结果:", result)
        case <-ctx.Done():
            fmt.Println("管道处理超时:", ctx.Err())
            return
        }
    }
}
```

## 最佳实践

### 1. 总是传递Context
```go
// 好的做法：函数接受Context参数
func processData(ctx context.Context, data []byte) error {
    // 检查取消
    if ctx.Err() != nil {
        return ctx.Err()
    }
    
    // 处理数据...
    return nil
}

// 不好的做法：没有Context参数
func processDataBad(data []byte) error {
    // 无法响应取消信号
    return nil
}
```

### 2. 不要存储Context
```go
// 错误：不要在结构体中存储Context
type BadService struct {
    ctx context.Context // 不要这样做
}

// 正确：将Context作为方法参数传递
type GoodService struct {
    // 其他字段
}

func (s *GoodService) Process(ctx context.Context, data string) error {
    // 使用传入的Context
    return nil
}
```

### 3. 使用defer cancel()
```go
func goodPractice() {
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel() // 确保资源被释放
    
    // 使用ctx...
}
```

### 4. 检查Context错误
```go
func handleContextError(ctx context.Context) {
    select {
    case <-ctx.Done():
        switch ctx.Err() {
        case context.Canceled:
            fmt.Println("操作被取消")
        case context.DeadlineExceeded:
            fmt.Println("操作超时")
        default:
            fmt.Println("未知错误:", ctx.Err())
        }
    default:
        // 继续处理
    }
}
```

## 面试要点

### 核心问题
1. **Context的作用？**
   - 传递取消信号
   - 设置超时时间
   - 传递请求范围的值

2. **Context的类型？**
   - Background/TODO: 根Context
   - WithCancel: 可取消
   - WithTimeout/WithDeadline: 超时控制
   - WithValue: 传递值

3. **Context最佳实践？**
   - 总是作为第一个参数传递
   - 不要存储在结构体中
   - 使用defer cancel()避免泄漏
   - 检查Done()通道响应取消

4. **Context传播机制？**
   - 子Context继承父Context的取消信号
   - 父Context取消时，所有子Context也被取消
   - 子Context取消不影响父Context

### 常见误区
- 在结构体中存储Context
- 忘记调用cancel()函数
- 不检查Context.Done()通道
- 过度使用WithValue传递数据

### 一句话总结
> Context提供了在goroutine之间传递取消信号、超时控制和请求范围值的标准机制
