# Go死锁面试要点

## 死锁概念
死锁是多个goroutine相互等待对方释放资源，导致所有goroutine都无法继续执行的状态。

## 死锁四个必要条件

1. **互斥条件** - 资源不能被共享，同时只能被一个goroutine使用
2. **占有且等待** - goroutine持有资源的同时等待其他资源
3. **不可剥夺** - 资源不能被强制释放，只能主动释放
4. **循环等待** - 形成资源等待环路，如A等B，B等C，C等A

## Go中常见死锁场景

### 1. 互斥锁死锁
```go
var mu1, mu2 sync.Mutex

// Goroutine 1
mu1.Lock()
mu2.Lock()  // 等待mu2

// Goroutine 2
mu2.Lock()
mu1.Lock()  // 等待mu1，形成死锁
```

### 2. Channel死锁
```go
ch := make(chan int)
ch <- 1  // 无缓冲channel，没有接收者，死锁

// 或者
ch := make(chan int)
<-ch     // 没有发送者，死锁
```

## 死锁预防策略

### 1. 锁排序
```go
// 统一锁的获取顺序
func transfer(from, to *Account) {
    // 按地址排序，确保顺序一致
    if from < to {
        from.mu.Lock()
        to.mu.Lock()
    } else {
        to.mu.Lock()
        from.mu.Lock()
    }
    defer from.mu.Unlock()
    defer to.mu.Unlock()

    // 转账逻辑
}
```

### 2. 超时机制
```go
func tryLock(mu *sync.Mutex, timeout time.Duration) bool {
    ch := make(chan struct{})
    go func() {
        mu.Lock()
        ch <- struct{}{}
    }()

    select {
    case <-ch:
        return true
    case <-time.After(timeout):
        return false
    }
}
```

### 3. 避免嵌套锁
```go
// 不好：嵌套锁容易死锁
func badExample() {
    mu1.Lock()
    defer mu1.Unlock()

    mu2.Lock()  // 嵌套锁
    defer mu2.Unlock()
}

// 好：分离锁的使用
func goodExample() {
    mu1.Lock()
    data1 := getData1()
    mu1.Unlock()

    mu2.Lock()
    processData(data1)
    mu2.Unlock()
}
```

## 面试要点

### 核心问题
1. **死锁四个条件？** - 互斥、占有等待、不可剥夺、循环等待
2. **Go中如何避免死锁？** - 锁排序、超时机制、避免嵌套锁
3. **Channel死锁场景？** - 无缓冲channel没有对应的发送/接收操作

### 最佳实践
- 统一锁的获取顺序
- 使用defer确保锁的释放
- 避免长时间持有锁
- 使用Context实现超时控制