# Goroutine池设计与实现

## 为什么需要Goroutine池

### 问题背景
- **无限制创建**: 可能导致内存耗尽
- **频繁创建销毁**: 影响性能
- **资源竞争**: 过多goroutine争抢CPU资源
- **难以控制**: 无法限制并发数量

### 解决方案
通过预创建固定数量的goroutine来处理任务，实现资源复用和并发控制。

## 基本实现

### 简单版本
```go
type WorkerPool struct {
    workerCount int
    jobQueue    chan Job
    quit        chan bool
}

type Job struct {
    ID      int
    Handler func() error
}

func NewWorkerPool(workerCount, queueSize int) *WorkerPool {
    return &WorkerPool{
        workerCount: workerCount,
        jobQueue:    make(chan Job, queueSize),
        quit:        make(chan bool),
    }
}

func (wp *WorkerPool) Start() {
    for i := 0; i < wp.workerCount; i++ {
        go wp.worker(i)
    }
}

func (wp *WorkerPool) worker(id int) {
    for {
        select {
        case job := <-wp.jobQueue:
            fmt.Printf("Worker %d processing job %d\n", id, job.ID)
            job.Handler()
        case <-wp.quit:
            fmt.Printf("Worker %d stopping\n", id)
            return
        }
    }
}

func (wp *WorkerPool) Submit(job Job) {
    wp.jobQueue <- job
}

func (wp *WorkerPool) Stop() {
    close(wp.quit)
}
```

### 增强版本
```go
type EnhancedWorkerPool struct {
    workerCount int
    jobQueue    chan Job
    resultQueue chan Result
    wg          sync.WaitGroup
    ctx         context.Context
    cancel      context.CancelFunc
    stats       PoolStats
    mu          sync.RWMutex
}

type Result struct {
    JobID int
    Data  interface{}
    Error error
}

type PoolStats struct {
    TotalJobs     int64
    CompletedJobs int64
    FailedJobs    int64
    ActiveWorkers int32
}

func NewEnhancedWorkerPool(workerCount, queueSize int) *EnhancedWorkerPool {
    ctx, cancel := context.WithCancel(context.Background())
    
    return &EnhancedWorkerPool{
        workerCount: workerCount,
        jobQueue:    make(chan Job, queueSize),
        resultQueue: make(chan Result, queueSize),
        ctx:         ctx,
        cancel:      cancel,
    }
}

func (ewp *EnhancedWorkerPool) Start() {
    for i := 0; i < ewp.workerCount; i++ {
        ewp.wg.Add(1)
        go ewp.worker(i)
    }
}

func (ewp *EnhancedWorkerPool) worker(id int) {
    defer ewp.wg.Done()
    
    atomic.AddInt32(&ewp.stats.ActiveWorkers, 1)
    defer atomic.AddInt32(&ewp.stats.ActiveWorkers, -1)
    
    for {
        select {
        case job := <-ewp.jobQueue:
            result := Result{JobID: job.ID}
            
            err := job.Handler()
            if err != nil {
                result.Error = err
                atomic.AddInt64(&ewp.stats.FailedJobs, 1)
            } else {
                atomic.AddInt64(&ewp.stats.CompletedJobs, 1)
            }
            
            select {
            case ewp.resultQueue <- result:
            case <-ewp.ctx.Done():
                return
            }
            
        case <-ewp.ctx.Done():
            return
        }
    }
}

func (ewp *EnhancedWorkerPool) Submit(job Job) error {
    atomic.AddInt64(&ewp.stats.TotalJobs, 1)
    
    select {
    case ewp.jobQueue <- job:
        return nil
    case <-ewp.ctx.Done():
        return errors.New("pool is shutting down")
    }
}

func (ewp *EnhancedWorkerPool) GetResult() <-chan Result {
    return ewp.resultQueue
}

func (ewp *EnhancedWorkerPool) Stats() PoolStats {
    ewp.mu.RLock()
    defer ewp.mu.RUnlock()
    
    stats := ewp.stats
    stats.ActiveWorkers = atomic.LoadInt32(&ewp.stats.ActiveWorkers)
    return stats
}

func (ewp *EnhancedWorkerPool) Shutdown(timeout time.Duration) error {
    ewp.cancel()
    
    done := make(chan struct{})
    go func() {
        ewp.wg.Wait()
        close(done)
    }()
    
    select {
    case <-done:
        return nil
    case <-time.After(timeout):
        return errors.New("shutdown timeout")
    }
}
```

## 动态调整版本

### 自适应Worker池
```go
type AdaptiveWorkerPool struct {
    minWorkers    int
    maxWorkers    int
    currentWorkers int32
    jobQueue      chan Job
    workerQueue   chan chan Job
    quit          chan bool
    mu            sync.RWMutex
}

func NewAdaptiveWorkerPool(min, max, queueSize int) *AdaptiveWorkerPool {
    return &AdaptiveWorkerPool{
        minWorkers:  min,
        maxWorkers:  max,
        jobQueue:    make(chan Job, queueSize),
        workerQueue: make(chan chan Job, max),
        quit:        make(chan bool),
    }
}

func (awp *AdaptiveWorkerPool) Start() {
    // 启动最小数量的worker
    for i := 0; i < awp.minWorkers; i++ {
        awp.startWorker()
    }
    
    // 启动调度器
    go awp.dispatcher()
    
    // 启动监控器
    go awp.monitor()
}

func (awp *AdaptiveWorkerPool) dispatcher() {
    for {
        select {
        case job := <-awp.jobQueue:
            // 尝试分配给空闲worker
            select {
            case workerJobQueue := <-awp.workerQueue:
                workerJobQueue <- job
            default:
                // 没有空闲worker，尝试创建新worker
                if atomic.LoadInt32(&awp.currentWorkers) < int32(awp.maxWorkers) {
                    awp.startWorker()
                    workerJobQueue := <-awp.workerQueue
                    workerJobQueue <- job
                } else {
                    // 达到最大worker数，等待空闲worker
                    workerJobQueue := <-awp.workerQueue
                    workerJobQueue <- job
                }
            }
        case <-awp.quit:
            return
        }
    }
}

func (awp *AdaptiveWorkerPool) startWorker() {
    atomic.AddInt32(&awp.currentWorkers, 1)
    
    worker := &Worker{
        ID:          int(atomic.LoadInt32(&awp.currentWorkers)),
        JobQueue:    make(chan Job),
        WorkerQueue: awp.workerQueue,
        Quit:        make(chan bool),
    }
    
    go worker.Start()
}

type Worker struct {
    ID          int
    JobQueue    chan Job
    WorkerQueue chan chan Job
    Quit        chan bool
}

func (w *Worker) Start() {
    for {
        // 将自己的job队列注册到worker队列
        w.WorkerQueue <- w.JobQueue
        
        select {
        case job := <-w.JobQueue:
            job.Handler()
        case <-w.Quit:
            return
        }
    }
}
```

## 使用场景对比

### 1. 固定大小池
- **适用**: 负载稳定，资源可预测
- **优势**: 简单，资源使用可控
- **劣势**: 无法应对突发流量

### 2. 动态调整池
- **适用**: 负载波动大，需要弹性扩缩容
- **优势**: 资源利用率高，适应性强
- **劣势**: 实现复杂，调度开销大

### 3. 有界队列 vs 无界队列
```go
// 有界队列 - 背压控制
jobQueue := make(chan Job, 1000)

// 无界队列 - 可能内存溢出
jobQueue := make(chan Job)
```

## 面试要点

### 核心问题
1. **为什么需要Goroutine池？**
   - 控制并发数量，避免资源耗尽
   - 复用goroutine，减少创建销毁开销
   - 提供背压控制，防止系统过载

2. **如何设计Goroutine池？**
   - 任务队列：缓冲channel存储待处理任务
   - Worker池：固定数量的goroutine处理任务
   - 结果收集：可选的结果channel
   - 优雅关闭：使用context或quit channel

3. **关键设计考虑？**
   - 队列大小：平衡内存使用和背压控制
   - Worker数量：根据CPU核数和任务特性调整
   - 错误处理：任务失败不应影响worker
   - 监控统计：提供池的运行状态信息

4. **与直接创建Goroutine的区别？**
   - 资源控制：限制最大并发数
   - 性能优化：避免频繁创建销毁
   - 背压处理：队列满时可以拒绝或等待

### 最佳实践
- 根据任务特性选择合适的池大小
- 使用有界队列防止内存溢出
- 实现优雅关闭机制
- 提供监控和统计功能
- 考虑任务的优先级和超时处理

### 一句话总结
> Goroutine池通过预创建固定数量的goroutine来处理任务，实现并发控制和资源复用
